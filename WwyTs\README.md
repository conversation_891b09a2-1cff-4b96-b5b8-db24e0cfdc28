# 🎵 网易云音乐解析器 - 重构版

> 诗意般的音乐解析体验，现代化模块架构

## ✨ 项目特色

### 🎯 功能亮点
- **单曲解析**：支持短链接 `http://163cn.tv/xxx` 和标准链接解析
- **智能识别**：自动识别歌单、专辑、单曲分享内容
- **批量下载**：支持歌单和专辑批量下载，可选择音质
- **状态检测**：Cookie有效性和会员状态实时检测
- **诗意界面**：保持原有的文学美学设计风格

### 🏗️ 架构优势
- **模块化设计**：HTML/CSS/JS完全分离
- **智能解析引擎**：支持多种链接格式自动识别
- **错误处理机制**：完善的错误提示和异常处理
- **响应式设计**：完美适配桌面端和移动端

## 📁 项目结构

```
WwyTs/
├── index.html          # 主页面
├── css/
│   └── style.css       # 样式文件（保持诗意风格）
├── js/
│   ├── api.js          # API通信模块
│   ├── parser.js       # 智能解析模块
│   ├── ui.js           # UI控制模块
│   └── main.js         # 主逻辑模块
├── main.ts             # Deno服务器
└── README.md           # 项目说明
```

## 🚀 快速开始

### 环境要求
- Deno 1.30+
- 有效的网易云音乐Cookie

### 本地运行

1. **设置环境变量**
```bash
export NETEASE_COOKIE="MUSIC_U=your_music_u_value;os=pc;appver=8.9.75;"
```

2. **启动服务**
```bash
cd WwyTs
deno run --allow-net --allow-env --allow-read --allow-write main.ts
```

3. **访问应用**
```
http://localhost:3004
```

### Deno Deploy部署

1. 在Deno Deploy控制台设置环境变量 `NETEASE_COOKIE`
2. 部署main.ts文件
3. 访问分配的域名

## 🎵 使用指南

### 单曲解析
1. 切换到"单曲解析"选项卡
2. 粘贴网易云单曲分享内容，例如：
   ```
   分享Ni/Co's Covers/Ni/Co的单曲《where is the love?》: http://163cn.tv/Gng7M90
   ```
3. 选择音质，点击"解析单曲"
4. 点击"下载歌曲"开始下载

### 批量下载
1. 切换到"批量下载"选项卡
2. 粘贴歌单或专辑分享内容，例如：
   ```
   分享歌单: Step Off 别说话我听的到 https://music.163.com/m/playlist?id=**********&creatorId=**********
   ```
3. 系统自动识别类型和ID
4. 点击"加载歌曲列表"
5. 选择要下载的歌曲，点击"下载选中"

### 状态检测
1. 切换到"状态检测"选项卡
2. 点击"检测状态"
3. 查看Cookie有效性和会员状态

## 🔧 技术架构

### 前端模块

#### API模块 (api.js)
- 处理与后端的HTTP通信
- 支持请求超时和错误处理
- 提供批量下载和进度回调

#### 解析模块 (parser.js)
- 智能识别各种网易云音乐链接格式
- 支持短链接、标准链接、分享文本解析
- 提供ID验证和格式化功能

#### UI控制模块 (ui.js)
- 管理用户界面交互
- 处理选项卡切换和表单验证
- 提供进度显示和结果展示

#### 主逻辑模块 (main.js)
- 连接各个模块
- 处理主要业务逻辑
- 提供全局函数接口

### 后端服务 (main.ts)
- 基于Deno的现代化服务器
- 支持静态文件服务
- 提供RESTful API接口
- 智能短链接解析

## 🎨 设计理念

### 文学美学
保持原有的诗意设计风格，以《偷影子的人》为主题，营造孤独美学的视觉体验。

### 用户体验
- **智能识别**：用户只需粘贴分享内容，无需手动提取ID
- **实时反馈**：提供详细的操作状态和进度信息
- **错误友好**：清晰的错误提示和解决建议

### 技术先进性
- **模块化架构**：便于维护和扩展
- **现代化技术栈**：使用最新的Web标准
- **性能优化**：减少重复代码，提高执行效率

## 🔍 支持的链接格式

### 单曲
- 短链接：`http://163cn.tv/Gng7M90`
- 标准链接：`https://music.163.com/song?id=123456`
- 移动端链接：`https://music.163.com/m/song?id=123456`
- 直接ID：`123456789`

### 歌单
- 移动端：`https://music.163.com/m/playlist?id=**********&creatorId=**********`
- 桌面端：`https://music.163.com/playlist/**********/`
- 直接ID：`**********`

### 专辑
- 标准链接：`http://music.163.com/album/242274622/?userid=**********`
- 直接ID：`242274622`

## 📝 更新日志

### v2.0.0 (重构版)
- ✅ 完全重构为模块化架构
- ✅ 新增单曲解析功能
- ✅ 智能链接识别系统
- ✅ 短链接自动解析
- ✅ Cookie状态检测
- ✅ 响应式设计优化
- ✅ 错误处理机制完善

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

---

> "最难过的是看到你和我在一起，你却显得如此孤单" ——《偷影子的人》
