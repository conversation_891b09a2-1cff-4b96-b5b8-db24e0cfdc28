/**
 * 解析模块 - 智能识别和解析各种网易云音乐链接
 */

class MusicParser {
    constructor() {
        this.patterns = {
            // 单曲模式
            song: [
                // 短链接: http://163cn.tv/Gng7M90
                /163cn\.tv\/([a-zA-Z0-9]+)/i,
                // 标准单曲链接: https://music.163.com/song?id=123456
                /music\.163\.com\/song\?id=(\d+)/i,
                // 移动端单曲链接: https://music.163.com/m/song?id=123456
                /music\.163\.com\/m\/song\?id=(\d+)/i,
                // 单曲ID直接输入
                /^(\d{6,12})$/
            ],
            // 歌单模式
            playlist: [
                // 移动端歌单链接: https://music.163.com/m/playlist?id=7091014698&creatorId=1448699048
                /music\.163\.com\/m\/playlist\?id=(\d+)/i,
                // 桌面端歌单链接: https://music.163.com/playlist/7091014698/
                /music\.163\.com\/playlist\/(\d+)/i,
                // 歌单ID直接输入
                /^(\d{8,})$/
            ],
            // 专辑模式  
            album: [
                // 专辑链接: http://music.163.com/album/242274622/?userid=1448699048
                /music\.163\.com\/album\/(\d+)/i,
                // 专辑ID直接输入（较短的数字）
                /^(\d{6,9})$/
            ]
        };
    }

    /**
     * 智能解析输入内容
     */
    parseInput(input) {
        if (!input || !input.trim()) {
            return null;
        }

        const cleanInput = input.trim();
        
        // 尝试解析单曲
        const songResult = this.parseSong(cleanInput);
        if (songResult) {
            return songResult;
        }

        // 尝试解析歌单
        const playlistResult = this.parsePlaylist(cleanInput);
        if (playlistResult) {
            return playlistResult;
        }

        // 尝试解析专辑
        const albumResult = this.parseAlbum(cleanInput);
        if (albumResult) {
            return albumResult;
        }

        return null;
    }

    /**
     * 解析单曲
     */
    parseSong(input) {
        for (const pattern of this.patterns.song) {
            const match = input.match(pattern);
            if (match) {
                const id = match[1];
                
                // 如果是短链接，需要特殊处理
                if (pattern.source.includes('163cn.tv')) {
                    return {
                        type: 'song',
                        id: id,
                        isShortLink: true,
                        originalInput: input
                    };
                }
                
                return {
                    type: 'song',
                    id: id,
                    isShortLink: false,
                    originalInput: input
                };
            }
        }
        return null;
    }

    /**
     * 解析歌单
     */
    parsePlaylist(input) {
        for (const pattern of this.patterns.playlist) {
            const match = input.match(pattern);
            if (match) {
                return {
                    type: 'playlist',
                    id: match[1],
                    originalInput: input
                };
            }
        }
        return null;
    }

    /**
     * 解析专辑
     */
    parseAlbum(input) {
        for (const pattern of this.patterns.album) {
            const match = input.match(pattern);
            if (match) {
                return {
                    type: 'album',
                    id: match[1],
                    originalInput: input
                };
            }
        }
        return null;
    }

    /**
     * 从分享文本中提取信息
     */
    extractFromShareText(text) {
        // 提取歌曲名称
        const songNameMatch = text.match(/《([^》]+)》/);
        const songName = songNameMatch ? songNameMatch[1] : null;

        // 提取艺术家名称
        const artistMatch = text.match(/分享([^的]+)的|分享(.+?)《/);
        const artist = artistMatch ? (artistMatch[1] || artistMatch[2]) : null;

        // 提取专辑名称
        const albumMatch = text.match(/专辑《([^》]+)》/);
        const albumName = albumMatch ? albumMatch[1] : null;

        // 提取歌单名称
        const playlistMatch = text.match(/歌单:\s*([^\s]+)/);
        const playlistName = playlistMatch ? playlistMatch[1] : null;

        return {
            songName,
            artist,
            albumName,
            playlistName
        };
    }

    /**
     * 解析短链接（需要后端支持）
     */
    async resolveShortLink(shortCode) {
        try {
            // 这里需要调用后端API来解析短链接
            // 暂时返回短链接代码，让后端处理
            return shortCode;
        } catch (error) {
            console.error('解析短链接失败:', error);
            throw error;
        }
    }

    /**
     * 验证ID格式
     */
    validateId(id, type) {
        if (!id || typeof id !== 'string') {
            return false;
        }

        const numId = parseInt(id);
        if (isNaN(numId) || numId <= 0) {
            return false;
        }

        // 根据类型验证ID长度
        switch (type) {
            case 'song':
                return id.length >= 6 && id.length <= 12;
            case 'playlist':
                return id.length >= 8 && id.length <= 12;
            case 'album':
                return id.length >= 6 && id.length <= 10;
            default:
                return true;
        }
    }

    /**
     * 格式化解析结果
     */
    formatResult(result, extractedInfo = null) {
        if (!result) {
            return null;
        }

        const formatted = {
            ...result,
            extractedInfo: extractedInfo || this.extractFromShareText(result.originalInput)
        };

        // 验证ID
        if (!this.validateId(formatted.id, formatted.type)) {
            return null;
        }

        return formatted;
    }

    /**
     * 获取类型的中文名称
     */
    getTypeName(type) {
        const typeNames = {
            song: '单曲',
            playlist: '歌单',
            album: '专辑'
        };
        return typeNames[type] || type;
    }
}

// 创建全局解析器实例
window.musicParser = new MusicParser();
